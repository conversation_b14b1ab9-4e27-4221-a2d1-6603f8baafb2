<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="29">
            <item index="0" class="java.lang.String" itemvalue="dbt-common" />
            <item index="1" class="java.lang.String" itemvalue="referencing" />
            <item index="2" class="java.lang.String" itemvalue="python-daemon" />
            <item index="3" class="java.lang.String" itemvalue="dbt-sqlserver" />
            <item index="4" class="java.lang.String" itemvalue="pytimeparse" />
            <item index="5" class="java.lang.String" itemvalue="PyYAML" />
            <item index="6" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="7" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="8" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="9" class="java.lang.String" itemvalue="python-slugify" />
            <item index="10" class="java.lang.String" itemvalue="dbt-semantic-interfaces" />
            <item index="11" class="java.lang.String" itemvalue="sqlparse" />
            <item index="12" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="13" class="java.lang.String" itemvalue="dbt-postgres" />
            <item index="14" class="java.lang.String" itemvalue="dbt-adapters" />
            <item index="15" class="java.lang.String" itemvalue="jsonschema" />
            <item index="16" class="java.lang.String" itemvalue="botocore" />
            <item index="17" class="java.lang.String" itemvalue="dbt-core" />
            <item index="18" class="java.lang.String" itemvalue="oracledb" />
            <item index="19" class="java.lang.String" itemvalue="cryptography" />
            <item index="20" class="java.lang.String" itemvalue="jsonpointer" />
            <item index="21" class="java.lang.String" itemvalue="dbt-extractor" />
            <item index="22" class="java.lang.String" itemvalue="dbt-fabric" />
            <item index="23" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="24" class="java.lang.String" itemvalue="pandas" />
            <item index="25" class="java.lang.String" itemvalue="boto3" />
            <item index="26" class="java.lang.String" itemvalue="dbt-oracle" />
            <item index="27" class="java.lang.String" itemvalue="pytz" />
            <item index="28" class="java.lang.String" itemvalue="jsonpatch" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>