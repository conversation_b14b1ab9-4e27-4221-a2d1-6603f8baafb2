"""
This script fetches secrets from AWS Secrets Manager and exports selected keys to environment variables.
It uses a JSON configuration file to specify which secrets to fetch and which keys to export.
"""
import boto3
import os
import json
import argparse
from botocore.exceptions import ClientError

def get_secret(secret_name, region_name="us-east-1"):
    client = boto3.client("secretsmanager", region_name=region_name)
    try:
        response = client.get_secret_value(SecretId=secret_name)
        if "SecretString" in response:
            return json.loads(response["SecretString"])
        else:
            raise ValueError("Secret is not in string format.")
    except ClientError as e:
        print(f"Error retrieving {secret_name}: {e}")
        return {}

def export_selected_keys_to_env(secret_dict, keys):
    for key in keys:
        if key in secret_dict:
            os.environ[key] = secret_dict[key]
            print(f"Exported {key}")
        else:
            print(f"Key '{key}' not found in secret.")

def load_config(json_file_path):
    try:
        with open(json_file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Failed to load config file: {e}")
        exit(1)

def main(config_file, region):
    secrets_config = load_config(config_file)
    for entry in secrets_config:
        secret_name = entry["secret_name"]
        keys = entry["keys"]

        print(f"\nFetching secret: {secret_name}")
        secret_data = get_secret(secret_name, region)

        print(f"Exporting selected keys from {secret_name} to environment...")
        export_selected_keys_to_env(secret_data, keys)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Load secrets and set environment variables.")
    parser.add_argument("config_file", help="Path to the secrets config JSON file.")
    parser.add_argument("--region", default="us-east-1", help="AWS region (default: us-east-1)")

    args = parser.parse_args()
    main(args.config_file, args.region)
