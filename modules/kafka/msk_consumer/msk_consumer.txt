import json
import fastavro
import io
import argparse
import logging
from confluent_kafka import Consumer, Producer
from confluent_kafka.schema_registry import SchemaRegistryClient
from datetime import datetime, timedelta
from get_secrets import get_secret
from psycopg2.pool import <PERSON>Con<PERSON>ionPool
from typing import List, Dict, Optional, Any
from contextlib import contextmanager
import psutil
import setproctitle
from decimal import Decimal
from confluent_kafka.admin import AdminClient, NewTopic

logging.basicConfig(level=logging.ERROR, format="%(asctime)s - %(levelname)s - %(message)s")
CONFIG_FILE = "./config.json"
with open(CONFIG_FILE, "r", encoding="utf-8") as config_file:
    config = json.load(config_file)
postgres_credentials = get_secret('cdp-rds-postgres', 'me-central-1')
kafka_connection = get_secret('msk-uat', 'me-central-1')
KAFKA_BOOTSTRAP = kafka_connection["msk-bootstrap"]
SCHEMA_REGISTRY_URL = kafka_connection["schema-registry"]
TABLE_MAPPING = config["table_mapping"]
ENABLE_DISCARDED_MESSAGES_DB = config.get("enable_discarded_messages_db", True)
ENABLE_DISCARDED_MESSAGES_FILE = config.get("enable_discarded_messages_file", False)
POSTGRES_CONN = (
    f"dbname={postgres_credentials['kafka_target_db']} "
    f"user={postgres_credentials['dbt_user']} "
    f"password={postgres_credentials['dbt_password']} "
    f"host={postgres_credentials['dbt_host']} port=5432"
)
connection_pool = SimpleConnectionPool(1, 10, POSTGRES_CONN)

@contextmanager
def db_connection():
    conn = connection_pool.getconn()
    try:
        yield conn
    finally:
        connection_pool.putconn(conn)

cached_table_columns: Dict[str, List[str]] = {}
cached_primary_keys: Dict[str, List[str]] = {}

def fix_decimal_types(schema_dict):
    """
    Recursively modify the schema to handle only decimal logical types,
    leaving other logical types (like date) unchanged.
    Returns tuple of (modified_schema, decimal_fields) where decimal_fields
    contains information about fields that need decimal conversion.
    """
    decimal_fields = {}

    def _process_schema(schema_dict, path=""):
        if isinstance(schema_dict, dict):
            if schema_dict.get("logicalType") == "decimal":
                field_name = schema_dict.get("name", path.split(".")[-1] if path else "")
                decimal_fields[field_name] = {
                    "scale": schema_dict.get("scale", 0),
                    "path": path
                }
                return schema_dict

            if "fields" in schema_dict:
                for field in schema_dict["fields"]:
                    field_path = f"{path}.{field['name']}" if path else field['name']
                    _process_schema(field, field_path)

            if "type" in schema_dict:
                if isinstance(schema_dict["type"], list):
                    for t in schema_dict["type"]:
                        if isinstance(t, dict):
                            _process_schema(t, path)
                elif isinstance(schema_dict["type"], dict):
                    _process_schema(schema_dict["type"], path)

        return schema_dict

    modified_schema = _process_schema(schema_dict.copy())
    return modified_schema, decimal_fields

def convert_decimal_fields(record: dict, decimal_fields: dict) -> dict:
    """
    Convert decimal fields from bytes to Decimal objects or properly formatted values
    """
    logging.debug(f"Converting decimal fields. Known decimal fields: {list(decimal_fields.keys())}")

    for field_name, info in decimal_fields.items():
        if field_name in record:
            original_value = record[field_name]
            logging.debug(f"Processing decimal field '{field_name}': {type(original_value)}")

            if isinstance(original_value, bytes):
                record[field_name] = decode_avro_decimal(original_value, info["scale"])
                logging.debug(f"  Converted bytes to {type(record[field_name])}: {record[field_name]}")
            elif isinstance(original_value, dict) and 'value' in original_value and isinstance(original_value['value'], bytes):
                record[field_name] = decode_avro_decimal(original_value['value'], original_value.get('scale', info["scale"]))
                logging.debug(f"  Converted dict with bytes to {type(record[field_name])}: {record[field_name]}")

    for field_name, value in list(record.items()):
        if isinstance(value, dict) and 'scale' in value and 'value' in value and isinstance(value['value'], bytes):
            logging.debug(f"Found potential VariableScaleDecimal in field '{field_name}': {value}")
            try:
                original_value = record[field_name]
                record[field_name] = decode_variable_scale_decimal(value)
                logging.debug(f"  Converted VariableScaleDecimal from {type(original_value)} to {type(record[field_name])}: {record[field_name]}")
            except Exception as e:
                logging.error(f"Error decoding VariableScaleDecimal {field_name}: {e}")

    return record

def parse_schema_with_decimal(schema_dict):
    """
    Fixes the schema for decimal logical types and parses it using fastavro.
    Returns the parsed schema and decimal field information.
    """
    modified_schema, decimal_fields = fix_decimal_types(schema_dict)
    return fastavro.parse_schema(modified_schema), decimal_fields

def decode_avro_decimal(avro_bytes, scale):
    """
    Decodes an Avro decimal from its bytes representation.
    Avro stores decimals as big-endian signed integers.
    """
    if avro_bytes is None:
        return None

    sign = 1 if avro_bytes[0] < 128 else -1
    int_value = int.from_bytes(avro_bytes, byteorder="big", signed=True)

    decimal_value = Decimal(int_value) / (10 ** scale)
    return decimal_value

def decode_variable_scale_decimal(decimal_dict):
    """
    Decode a VariableScaleDecimal from Debezium.
    Format: {'scale': <scale>, 'value': <bytes>}
    """
    if not isinstance(decimal_dict, dict) or 'scale' not in decimal_dict or 'value' not in decimal_dict:
        logging.debug(f"Not a valid VariableScaleDecimal: {decimal_dict}")
        return decimal_dict

    try:
        scale = decimal_dict['scale']
        value_bytes = decimal_dict['value']
        logging.debug(f"Decoding VariableScaleDecimal with scale {scale} and value bytes: {value_bytes.hex() if isinstance(value_bytes, bytes) else value_bytes}")

        if not isinstance(value_bytes, bytes):
            logging.debug(f"Value is not bytes, can't decode: {type(value_bytes)}")
            return decimal_dict

        int_value = int.from_bytes(value_bytes, byteorder="big", signed=True)
        logging.debug(f"Converted bytes to int: {int_value}")

        if scale == 0:
            logging.debug(f"Scale is 0, returning int: {int_value}")
            return int_value
        else:
            decimal_value = Decimal(int_value) / (10 ** scale)
            logging.debug(f"Converted to Decimal with scale {scale}: {decimal_value}")
            return decimal_value
    except Exception as e:
        logging.error(f"Failed to decode VariableScaleDecimal: {e}")
        return f"Decimal({decimal_dict.get('value', '').hex() if isinstance(decimal_dict.get('value'), bytes) else decimal_dict.get('value')}, scale={decimal_dict.get('scale')})"

def get_table_columns(schema_name: str, table_name: str) -> List[str]:
    """
    Retrieves the column names for a given schema.table from PostgreSQL,
    caching the results for future lookups.
    """
    cache_key = f"{schema_name}.{table_name}"
    if cache_key in cached_table_columns:
        logging.info(f"Using cached table columns for {schema_name}.{table_name}: {cached_table_columns[cache_key]}")
        return cached_table_columns[cache_key]

    query = """
    SELECT column_name
    FROM information_schema.columns
    WHERE table_schema = %s AND table_name = %s
    ORDER BY ordinal_position;
    """
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (schema_name, table_name))
                columns = [row[0] for row in cursor.fetchall()]
                cached_table_columns[cache_key] = columns
                logging.debug(f"Retrieved table columns for {schema_name}.{table_name}: {columns}")
        return columns
    except Exception as e:
        logging.error(f"Error fetching table columns for {schema_name}.{table_name}: {e}")
        return []

def get_primary_key(schema_name: str, table_name: str) -> List[str]:
    """
    Retrieves the primary key column names for a given schema.table from PostgreSQL,
    caching the results for future lookups.
    """
    cache_key = f"{schema_name}.{table_name}"
    if cache_key in cached_primary_keys:
        logging.debug(f"Using cached primary keys for {schema_name}.{table_name}: {cached_primary_keys[cache_key]}")
        return cached_primary_keys[cache_key]

    query = """
    SELECT a.attname
    FROM pg_index i
    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
    JOIN pg_class c ON i.indrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = %s AND c.relname = %s AND i.indisprimary;
    """
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (schema_name, table_name))
                primary_keys = [row[0] for row in cursor.fetchall()]
                cached_primary_keys[cache_key] = primary_keys
                logging.debug(f"Retrieved primary keys for {schema_name}.{table_name}: {primary_keys}")
        return primary_keys
    except Exception as e:
        logging.error(f"Error fetching primary key columns for {schema_name}.{table_name}: {e}")
        return []

def get_local_schema(schema_file: str) -> Optional[Dict]:
    """
    Loads an Avro schema from a local JSON file.
    """
    try:
        with open(schema_file, "r", encoding="utf-8") as f:
            schema = json.load(f)
            logging.info(f"Loaded local schema from {schema_file}")
            return schema
    except Exception as e:
        logging.error(f"Failed to load local schema from {schema_file}: {e}")
        return None

def fetch_schema_from_registry(topic: str) -> Optional[Dict]:
    """
    Fetches and returns the Avro schema from the Confluent Schema Registry
    for a given topic using the Schema Registry client.
    """
    try:
        schema_registry_conf = {'url': SCHEMA_REGISTRY_URL}
        schema_registry_client = SchemaRegistryClient(schema_registry_conf)

        subject_name = f"{topic}-value"
        schema = schema_registry_client.get_latest_version(subject_name)
        schema_str = schema.schema.schema_str

        logging.info(f"Fetched schema from registry for topic {topic}")
        return json.loads(schema_str)
    except Exception as e:
        logging.error(f"Failed to retrieve schema from Schema Registry for topic {topic}: {e}")
        return None

def log_discarded_message_db(message: Dict, reason: str, topic: str, offset: int) -> None:
    """
    Logs a discarded message into a PostgreSQL table if DB logging is enabled.
    """
    try:
        serializable_message = make_json_serializable(message)

        with db_connection() as conn:
            with conn.cursor() as cursor:
                query = """
                INSERT INTO ops.discarded_messages (message, error, topic, message_offset, created_at)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(query, (json.dumps(serializable_message, cls=BytesEncoder), reason, topic, offset, datetime.now()))
            conn.commit()
        logging.info(f"Logged discarded message to DB for topic {topic} at offset {offset}")
    except Exception as e:
        logging.error(f"Error logging discarded message to DB: {e}")

def get_discarded_messages_filename(table_name: str) -> str:
    """
    Generate the discarded messages filename based on the table name.
    Replaces dots with underscores to avoid issues with file paths.
    """
    safe_table_name = table_name.replace('.', '_')
    return f"{safe_table_name}_discarded_messages.txt"

def log_discarded_message_file(message: Dict, reason: str, topic: str, offset: int, discarded_messages_file: str) -> None:
    """
    Logs a discarded message into a table-specific local file if file logging is enabled.
    """
    try:
        serializable_message = make_json_serializable(message)

        with open(discarded_messages_file, "a", encoding="utf-8") as f:
            log_entry = f"{datetime.now()} - Topic: {topic} - Offset: {offset} - Reason: {reason} - Message: {json.dumps(serializable_message, cls=BytesEncoder)}\n"
            f.write(log_entry)
        logging.info(f"Logged discarded message to file {discarded_messages_file} for topic {topic} at offset {offset}")
    except Exception as e:
        logging.error(f"Error logging discarded message to file {discarded_messages_file} at offset {offset}: {e}")
        try:
            with open(discarded_messages_file, "a", encoding="utf-8") as f:
                log_entry = f"{datetime.now()} - Topic: {topic} - Offset: {offset} - Reason: {reason} - Message: [Error serializing message: {e}]\n"
                f.write(log_entry)
        except Exception as fallback_error:
            logging.error(f"Even fallback logging failed: {fallback_error}")

def handle_discarded_message(message: Dict, reason: str, topic: str, offset: int, discarded_messages_file: str) -> None:
    """
    Dispatches discarded messages to DB logging or file logging, as enabled in config.
    """
    if ENABLE_DISCARDED_MESSAGES_DB:
        log_discarded_message_db(message, reason, topic, offset)
    if ENABLE_DISCARDED_MESSAGES_FILE:
        log_discarded_message_file(message, reason, topic, offset, discarded_messages_file)

def process_message(msg_value: bytes, avro_schema: dict) -> Dict:
    """
    Process a single message using the provided Avro schema
    """
    logging.debug(f"Processing message with {len(msg_value)} bytes")

    parsed_schema, decimal_fields = parse_schema_with_decimal(avro_schema)
    logging.debug(f"Parsed schema and found {len(decimal_fields)} decimal fields")

    bytes_reader = io.BytesIO(msg_value)
    record = fastavro.schemaless_reader(bytes_reader, parsed_schema)
    logging.debug(f"Decoded Avro message with {len(record)} fields: {list(record.keys())}")

    record = convert_decimal_fields(record, decimal_fields)
    logging.debug("Decimal fields converted")

    record = convert_date_fields(record, avro_schema)
    logging.debug("Date fields converted")

    sample_fields = {k: str(v)[:100] for k, v in list(record.items())[:5]}
    logging.debug(f"Processed record sample (first 5 fields): {sample_fields}")

    return record

def insert_batch_into_postgres(batch: List[Dict], schema_name: str, table_name: str, table_columns: List[str], 
                              topic: str, discarded_messages_file: str, dlq_producer: Producer = None) -> None:
    if not batch:
        return

    failed_records = []
    successful_records = []
    
    try:
        all_values = []
        successful_indices = []
        preprocessing_failed_records = []
        
        for idx, data in enumerate(batch):
            try:
                all_values.append(values)
                successful_indices.append(idx)
            except Exception as e:
                offset = data.get('_kafka_offset')
                preprocessing_failed_records.append((data, str(e)))
                logging.error(f"Failed to prepare record {idx} for batch at offset {offset}: {e}")
                
                if dlq_producer and '_raw_message' in data:
                    send_to_dlq(
                        raw_message=data['_raw_message'],
                        topic=topic,
                        producer=dlq_producer
                    )
                
                handle_discarded_message(
                    message=data,
                    reason=f"Batch preparation failed: {e}",
                    topic=topic,
                    offset=offset,
                    discarded_messages_file=discarded_messages_file
                )
        

        logging.info("Falling back to individual record processing")
        successful_count = 0
        for idx, values in zip(successful_indices, all_values):
            try:
                cursor.execute(query, values)
                conn.commit()
                successful_records.append(batch[idx])
                successful_count += 1
            except Exception as record_error:
                offset = batch[idx].get('_kafka_offset')
                failed_records.append((batch[idx], str(record_error)))
                logging.error(f"Failed to process record {idx} individually at offset {offset}: {record_error}")
                
                if dlq_producer and '_raw_message' in batch[idx]:
                    send_to_dlq(
                        raw_message=batch[idx]['_raw_message'],
                        topic=topic,
                        producer=dlq_producer
                    )
                
                handle_discarded_message(
                    message=batch[idx],
                    reason=f"Individual insert failed: {record_error}",
                    topic=topic,
                    offset=offset,
                    discarded_messages_file=discarded_messages_file
                )
    
    except Exception as e:
        remaining_records = [record for record in batch if record not in successful_records]
        logging.error(f"❌ Critical error during batch processing for {schema_name}.{table_name}: {str(e)}")
        logging.error(f"Number of unprocessed records due to critical error: {len(remaining_records)}")

        for record in remaining_records:
            offset = record.get('_kafka_offset')
            
            if dlq_producer and '_raw_message' in record:
                send_to_dlq(
                    raw_message=record['_raw_message'],
                    topic=topic,
                    producer=dlq_producer
                )
            
            handle_discarded_message(
                message=record,
                reason=f"Critical batch processing error: {e}",
                topic=topic,
                offset=offset,
                discarded_messages_file=discarded_messages_file
            )

def get_config_value(table_config: Dict, global_config: Dict, key: str, default: Any = None) -> Any:
    """
    Safely retrieve a config value from the table's config if it exists,
    otherwise fall back to the global config, then the provided default.
    """
    return table_config.get(key, global_config.get(key, default))

def set_process_name(table_name: str, config: Dict) -> str:
    """
    Sets a unique process name for the consumer using setproctitle, based on the
    table name and the consumer group ID. Also increments the instance number if
    multiple consumers with the same base name are running.
    """
    table_config = config["table_mapping"][table_name]
    group_id = table_config.get("group-id", "data-platform")
    base_name = f"kafka_consumer_{table_name}_{group_id}"
    instance_num = sum(
        1 for proc in psutil.process_iter(['name'])
        if proc.info['name'] and proc.info['name'].startswith(base_name)
    ) + 1
    process_name = f"{base_name}_{instance_num}"
    setproctitle.setproctitle(process_name)
    logging.info(f"Set process name to: {process_name}")
    logging.info(f"Consuming from topic: {table_config['topic']}")
    return process_name

def consume_messages(table_name: str, use_local_schema: bool) -> None:
    if table_name not in TABLE_MAPPING:
        logging.error(f"Error: Table '{table_name}' not found in configuration.")
        return

    process_name = set_process_name(table_name, config)
    logging.info(f"Started consumer process: {process_name}")

    discarded_messages_file = get_discarded_messages_filename(table_name)
    logging.info(f"Using discarded messages file: {discarded_messages_file}")

    schema_name, tbl = table_name.split(".")
    table_config = TABLE_MAPPING[table_name]
    topic = table_config["topic"]
    
    dlq_producer = None
    if ENABLE_DLQ:
        logging.info(f"DLQ is enabled. Creating DLQ producer and ensuring DLQ topic exists.")
        dlq_producer = Producer({
            "bootstrap.servers": KAFKA_BOOTSTRAP,
            "security.protocol": "PLAINTEXT"
        })
        ensure_dlq_topic_exists(topic)
    else:
        logging.info("DLQ is disabled. Messages will not be sent to DLQ.")
    

    with db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """, (schema_name, tbl))
            table_columns = [row[0] for row in cursor.fetchall()]

    if not table_columns:
        logging.error(f"No columns found for table {schema_name}.{tbl}")
        return

    local_schema_file = table_config.get("local_schema_file", "")
    BATCH_SIZE = table_config.get("write_batch_size", 500)
    BATCH_WAIT_TIME = table_config.get("batch_wait", 2.0)
    if use_local_schema:
        if not local_schema_file:
            logging.error("Local schema file not provided in configuration while --use-local-schema flag is used.")
            return
        schema_dict = get_local_schema(local_schema_file)
        if schema_dict:
            avro_schema, _ = parse_schema_with_decimal(schema_dict)
        else:
            logging.error(f"Failed to load local schema from file {local_schema_file} for topic {topic}")
            return
    else:
        fetched = fetch_schema_from_registry(topic)
        if fetched:
            avro_schema, _ = parse_schema_with_decimal(fetched)
        else:
            logging.error(f"Failed to fetch schema for topic {topic}")
            return

    logging.info(f"Starting consumer for topic: {topic} and table: {schema_name}.{tbl}")
    logging.info(f"Using batch size: {BATCH_SIZE} and batch wait time: {BATCH_WAIT_TIME} seconds")

    consumer = Consumer({
        "bootstrap.servers": KAFKA_BOOTSTRAP,
        "group.id": table_config.get("group_id", "data-platform"),
        "auto.offset.reset": "earliest",
        "security.protocol": "PLAINTEXT",
        "enable.auto.commit": False
    })

    consumer.subscribe([topic])
    batch = []
    last_batch_time = datetime.now()

    try:
        while True:
            msg = consumer.poll(timeout=1.0)

            if msg is None:
                if batch and (datetime.now() - last_batch_time).seconds >= BATCH_WAIT_TIME:
                    logging.info(f"Processing batch of {len(batch)} messages due to timeout")
                    insert_batch_into_postgres(
                        batch,
                        schema_name,
                        tbl,
                        table_columns,
                        topic,
                        discarded_messages_file,
                        dlq_producer
                    )
                    consumer.commit()
                    batch.clear()
                    last_batch_time = datetime.now()
                continue

            if msg.error():
                logging.error(f"Kafka error at offset {msg.offset() if msg else 'unknown'}: {msg.error()}")
                continue

            try:
                logging.debug(f"Processing message at offset {msg.offset()}")
                avro_bytes = msg.value()
                raw_message = avro_bytes
                
                if len(avro_bytes) < 5:
                    raise ValueError(f"Message too short for Confluent Avro: {len(avro_bytes)} bytes")
                
                payload = avro_bytes[5:]
                decoded_message = process_message(payload, avro_schema)
                
                decoded_message['_kafka_offset'] = msg.offset()
                decoded_message['_raw_message'] = raw_message
                
                batch.append(decoded_message)
                logging.debug(f"Added message to batch. Current batch size: {len(batch)}")
                
            except Exception as decode_error:
                logging.error(f"Failed to decode message at offset {msg.offset()}: {decode_error}")
                logging.error(f"Raw message: {msg.value().hex() if msg.value() else 'None'}")
                
                if dlq_producer:
                    send_to_dlq(
                        raw_message=msg.value(),
                        topic=topic,
                        producer=dlq_producer
                    )
                
                handle_discarded_message(
                    {"raw": msg.value().hex() if msg.value() else "None"},
                    f"Decoding error: {decode_error}",
                    topic,
                    msg.offset(),
                    discarded_messages_file
                )
                continue

            if len(batch) >= BATCH_SIZE:
                logging.info(f"Processing batch of {len(batch)} messages (batch size limit reached)")
                insert_batch_into_postgres(
                    batch,
                    schema_name,
                    tbl,
                    table_columns,
                    topic,
                    discarded_messages_file,
                    dlq_producer
                )
                consumer.commit()
                batch.clear()
                last_batch_time = datetime.now()
    except KeyboardInterrupt:
        logging.info("Consumer stopped by keyboard interrupt.")
    except Exception as e:
        logging.error(f"Unexpected error in consumer loop: {e}")
    finally:
        if batch:
            insert_batch_into_postgres(
                batch, 
                schema_name, 
                tbl, 
                table_columns, 
                topic, 
                discarded_messages_file,
                dlq_producer
            )
            consumer.commit()
        consumer.close()
        if dlq_producer:
            dlq_producer.flush()

def adapt_value_for_postgres(value):
    """
    Convert Python values to PostgreSQL-compatible types.
    """
    original_type = type(value).__name__

    if isinstance(value, dict):
        if 'scale' in value and 'value' in value and isinstance(value['value'], bytes):
            try:
                adapted = decode_variable_scale_decimal(value)
                logging.debug(f"Adapted VariableScaleDecimal from {original_type} to {type(adapted).__name__}: {adapted}")
                return adapted
            except Exception as e:
                logging.error(f"Error adapting VariableScaleDecimal: {e}")
                return str(value)
        adapted = json.dumps(value, cls=BytesEncoder)
        logging.debug(f"Adapted dict from {original_type} to string: {adapted[:100]}...")
        return adapted
    elif isinstance(value, list):
        adapted = json.dumps(value, cls=BytesEncoder)
        logging.debug(f"Adapted list from {original_type} to string: {adapted[:100]}...")
        return adapted
    elif isinstance(value, bytes):
        adapted = value.hex()
        logging.debug(f"Adapted bytes from {original_type} to hex string: {adapted[:100]}...")
        return adapted
    elif isinstance(value, Decimal):
        adapted = float(value)
        logging.debug(f"Adapted Decimal from {original_type} to float: {adapted}")
        return adapted

    logging.debug(f"No adaptation needed for {original_type}: {str(value)[:100]}")
    return value

def convert_date_fields(record, schema_dict):
    """
    Convert date and timestamp fields to appropriate PostgreSQL formats.
    """
    logging.debug(f"Converting date fields in record with keys: {list(record.keys())}")

    for field in schema_dict.get('fields', []):
        field_name = field.get('name')
        field_type = field.get('type')

        if field_name not in record or record[field_name] is None:
            continue

        logging.debug(f"Checking field '{field_name}' with type: {field_type}")

        if isinstance(field_type, list):
            for type_option in field_type:
                if isinstance(type_option, dict) and 'logicalType' in type_option:
                    logical_type = type_option.get('logicalType')
                    logging.debug(f"  Found logical type in union: {logical_type}")

                    if logical_type == 'date' and isinstance(record[field_name], int):
                        original_value = record[field_name]
                        record[field_name] = (datetime(1970, 1, 1) + timedelta(days=record[field_name])).strftime('%Y-%m-%d')
                        logging.debug(f"  Converted date from {original_value} to {record[field_name]}")
                    elif logical_type == 'timestamp-millis' and isinstance(record[field_name], int):
                        original_value = record[field_name]
                        record[field_name] = datetime.fromtimestamp(record[field_name] / 1000).strftime('%Y-%m-%d %H:%M:%S')
                        logging.debug(f"  Converted timestamp from {original_value} to {record[field_name]}")

        elif isinstance(field_type, dict) and 'logicalType' in field_type:
            logical_type = field_type.get('logicalType')
            logging.debug(f"  Found direct logical type: {logical_type}")

            if logical_type == 'date' and isinstance(record[field_name], int):
                original_value = record[field_name]
                record[field_name] = (datetime(1970, 1, 1) + timedelta(days=record[field_name])).strftime('%Y-%m-%d')
                logging.debug(f"  Converted date from {original_value} to {record[field_name]}")
            elif logical_type == 'timestamp-millis' and isinstance(record[field_name], int):
                original_value = record[field_name]
                record[field_name] = datetime.fromtimestamp(record[field_name] / 1000).strftime('%Y-%m-%d %H:%M:%S')
                logging.debug(f"  Converted timestamp from {original_value} to {record[field_name]}")

    return record

class BytesEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles bytes, Decimal objects, and other non-serializable types."""
    def default(self, obj):
        if isinstance(obj, bytes):
            return obj.hex()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'hex'):
            return obj.hex()
        elif hasattr(obj, '__str__'):
            return str(obj)
        return super().default(obj)

def make_json_serializable(obj):
    """
    Recursively convert a dictionary with non-serializable values to a serializable format.
    """
    if isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(make_json_serializable(item) for item in obj)
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, bytes):
        return obj.hex()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif obj is None or isinstance(obj, (str, int, float, bool)):
        return obj
    else:
        return str(obj)

def get_column_types(schema_name: str, table_name: str) -> Dict[str, str]:
    """
    Retrieves the column types for a given schema.table from PostgreSQL,
    caching the results for future lookups.
    """
    cache_key = f"{schema_name}.{table_name}.types"
    if cache_key in cached_table_columns:
        return cached_table_columns[cache_key]

    query = """
    SELECT column_name, data_type
    FROM information_schema.columns
    WHERE table_schema = %s AND table_name = %s;
    """
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (schema_name, table_name))
                column_types = {row[0].lower(): row[1] for row in cursor.fetchall()}
                cached_table_columns[cache_key] = column_types
                logging.debug(f"Retrieved column types for {schema_name}.{table_name}: {column_types}")
        return column_types
    except Exception as e:
        logging.error(f"Error fetching column types for {schema_name}.{table_name}: {e}")
        return {}


ENABLE_DLQ = True
DLQ_SUFFIX = "-DLQ"

def get_dlq_topic_name(source_topic: str) -> str:
    """
    Generates the DLQ topic name based on the source topic.
    
    Args:
        source_topic: The original topic name
        
    Returns:
        The DLQ topic name in the format {source_topic}-DLQ
    """
    return f"{source_topic}{DLQ_SUFFIX}"

def send_to_dlq(raw_message: bytes, topic: str, producer: Producer) -> None:
    if not ENABLE_DLQ:
        logging.debug("DLQ is disabled, skipping")
        return
    
    dlq_topic = get_dlq_topic_name(topic)
    
    try:
        producer.produce(
            dlq_topic,
            value=raw_message,
            callback=lambda err, msg: logging.info(f"Raw message sent to DLQ: {msg.offset()}") if err is None else logging.error(f"Failed to send to DLQ: {err}")
        )
        logging.info(f"Sent raw message to DLQ topic {dlq_topic}")
    except Exception as e:
        logging.error(f"Error sending raw message to DLQ: {e}")

def ensure_dlq_topic_exists(source_topic: str) -> None:
    if not ENABLE_DLQ:
        return
    
    dlq_topic = get_dlq_topic_name(source_topic)
    logging.info(f"Checking if DLQ topic '{dlq_topic}' exists...")
        
    try:
        admin_client = AdminClient({
            "bootstrap.servers": KAFKA_BOOTSTRAP,
            "security.protocol": "PLAINTEXT"
        })
        
        metadata = admin_client.list_topics(timeout=10)
        topics = metadata.topics
        
        if dlq_topic not in topics:
            logging.info(f"DLQ topic '{dlq_topic}' does not exist. Creating it...")
            
            topic_list = [
                NewTopic(
                    dlq_topic,
                    num_partitions=3,
                    replication_factor=2,
                    config={
                        "retention.ms": str(14 * 24 * 60 * 60 * 1000),
                        "cleanup.policy": "delete"
                    }
                )
            ]
            
            fs = admin_client.create_topics(topic_list)
            
            for topic, f in fs.items():
                try:
                    f.result()
                    logging.info(f"Topic {topic} created successfully")
                except Exception as e:
                    if "already exists" in str(e):
                        logging.info(f"Topic {topic} already exists (created by another process)")
                    else:
                        logging.error(f"Failed to create topic {topic}: {e}")
                        raise
        else:
            logging.info(f"DLQ topic '{dlq_topic}' already exists")
            
    except Exception as e:
        logging.error(f"Error ensuring DLQ topic exists: {e}")
        logging.warning(f"Will continue without DLQ topic creation. Messages may be lost if the topic {dlq_topic} doesn't exist.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Kafka Consumer with Schema Source Option")
    parser.add_argument("table_name", type=str, help="Target table name in the format schema.table")
    parser.add_argument("--use-local-schema", action="store_true", help="Load schema from local .avsc file instead of Schema Registry")
    args = parser.parse_args()
    
    consume_messages(args.table_name, args.use_local_schema)
