def log_discarded_message_db(message: Dict, reason: str, topic: str, offset: int) -> None:
    """
    Logs a discarded message into a PostgreSQL table if DB logging is enabled.
    """
    try:
        serializable_message = make_json_serializable(message)

        with db_connection() as conn:
            with conn.cursor() as cursor:
                query = """
                INSERT INTO ops.discarded_messages (message, error, topic, message_offset, created_at)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(query, (json.dumps(serializable_message, cls=BytesEncoder), reason, topic, offset, datetime.now()))
            conn.commit()
        logging.info(f"Logged discarded message to <PERSON> for topic {topic} at offset {offset}")
    except Exception as e:
        logging.error(f"Error logging discarded message to DB: {e}")