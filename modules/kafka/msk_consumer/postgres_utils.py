import io
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Optional
from contextlib import contextmanager
from fastavro import parse_schema, schemaless_reader
from psycopg2.pool import SimpleConnectionPool
from config_loader import get_config
from logging_utils import handle_discarded_message
from secrets_manager import get_postgres_credentials

config = get_config()
creds = get_postgres_credentials()
POSTGRES_CONN = f"dbname={creds['kafka_target_db']} user={creds['dbt_user']} password={creds['dbt_password']} host={creds['dbt_host']} port=5432"
connection_pool = SimpleConnectionPool(1, 10, POSTGRES_CONN)

@contextmanager
def db_connection():
    conn = connection_pool.getconn()
    try:
        yield conn
    finally:
        connection_pool.putconn(conn)

cached_table_columns: Dict[str, List[str]] = {}

def get_table_columns(schema_name: str, table_name: str) -> List[str]:
    cache_key = f"{schema_name}.{table_name}"
    if cache_key in cached_table_columns:
        return cached_table_columns[cache_key]
    query = """
        SELECT column_name FROM information_schema.columns
        WHERE table_schema = %s AND table_name = %s
        ORDER BY ordinal_position;
    """
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (schema_name, table_name))
                columns = [row[0] for row in cursor.fetchall()]
                cached_table_columns[cache_key] = columns
                return columns
    except Exception as e:
        logging.error(f"Error fetching table columns: {e}")
        return []

def insert_batch_into_postgres(batch: List[Dict], schema_name: str, table_name: str, table_columns: List[str], topic: str, discarded_messages_file: str, dlq_producer = None) -> None:
    if not batch:
        return

    placeholders = ','.join(['%s'] * len(table_columns))
    columns = ','.join(table_columns)
    query = f"INSERT INTO {schema_name}.{table_name} ({columns}) VALUES ({placeholders})"

    with db_connection() as conn:
        with conn.cursor() as cursor:
            for record in batch:
                try:
                    values = [adapt_value_for_postgres(record.get(col)) for col in table_columns]
                    cursor.execute(query, values)
                except Exception as e:
                    logging.error(f"Insert failed for offset {record.get('_kafka_offset')}: {e}")
                    handle_discarded_message(record, str(e), topic, record.get('_kafka_offset'), discarded_messages_file)
            conn.commit()

def process_message(msg_value: bytes, avro_schema: dict) -> Dict:
    parsed_schema, decimal_fields = parse_schema_with_decimal(avro_schema)
    record = schemaless_reader(io.BytesIO(msg_value), parsed_schema)
    record = convert_decimal_fields(record, decimal_fields)
    return convert_date_fields(record, avro_schema)

def adapt_value_for_postgres(value):
    if isinstance(value, dict):
        return json.dumps(value)
    elif isinstance(value, list):
        return json.dumps(value)
    elif isinstance(value, bytes):
        return value.hex()
    elif isinstance(value, Decimal):
        return float(value)
    return value

def parse_schema_with_decimal(schema_dict):
    modified_schema, decimal_fields = fix_decimal_types(schema_dict)
    return parse_schema(modified_schema), decimal_fields

def fix_decimal_types(schema_dict):
    decimal_fields = {}
    def _walk(schema, path=""):
        if isinstance(schema, dict):
            if schema.get("logicalType") == "decimal":
                field = path.split(".")[-1]
                decimal_fields[field] = {"scale": schema.get("scale", 0), "path": path}
            if "fields" in schema:
                for f in schema["fields"]:
                    _walk(f, f["name"] if not path else f"{path}.{f['name']}")
            if isinstance(schema.get("type"), dict):
                _walk(schema["type"], path)
            elif isinstance(schema.get("type"), list):
                for t in schema["type"]:
                    _walk(t, path)
        return schema
    return _walk(schema_dict.copy()), decimal_fields

def convert_decimal_fields(record: dict, decimal_fields: dict) -> dict:
    for field, info in decimal_fields.items():
        val = record.get(field)
        if isinstance(val, bytes):
            record[field] = decode_avro_decimal(val, info["scale"])
    return record

def decode_avro_decimal(avro_bytes, scale):
    if avro_bytes is None:
        return None
    int_value = int.from_bytes(avro_bytes, byteorder="big", signed=True)
    return Decimal(int_value) / (10 ** scale)

def convert_date_fields(record, schema_dict):
    for field in schema_dict.get("fields", []):
        fname = field["name"]
        ftype = field["type"]
        if isinstance(ftype, list):
            for t in ftype:
                if isinstance(t, dict) and t.get("logicalType") == "date" and isinstance(record.get(fname), int):
                    record[fname] = (datetime(1970,1,1) + timedelta(days=record[fname])).strftime("%Y-%m-%d")
        elif isinstance(ftype, dict) and ftype.get("logicalType") == "date" and isinstance(record.get(fname), int):
            record[fname] = (datetime(1970,1,1) + timedelta(days=record[fname])).strftime("%Y-%m-%d")
    return record
