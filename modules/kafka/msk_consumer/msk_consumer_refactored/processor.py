from typing import Dict, Any, List, Callable, Optional
import io
import json
import logging
from datetime import datetime
from decimal import Decimal
import fastavro

class MessageProcessor:
    def __init__(self, schema: Dict[str, Any]):
        self.schema = schema
        self.parsed_schema, self.decimal_fields = self._parse_schema()
        self.processors = []
    
    def _parse_schema(self):
        """Parse the Avro schema and identify decimal fields"""
        parsed_schema = fastavro.parse_schema(self.schema)
        decimal_fields = self._find_decimal_fields(self.schema)
        return parsed_schema, decimal_fields
    
    def _find_decimal_fields(self, schema, prefix=""):
        """Recursively find decimal fields in the schema"""
        decimal_fields = []
        # Implementation details...
        return decimal_fields
    
    def add_processor(self, processor: Callable[[Dict[str, Any]], Dict[str, Any]]):
        """Add a processing step to the pipeline"""
        self.processors.append(processor)
        return self
    
    def process(self, raw_message: bytes) -> Dict[str, Any]:
        """Process a raw Kafka message through the entire pipeline"""
        try:
            # Decode Avro message
            bytes_reader = io.BytesIO(raw_message)
            record = fastavro.schemaless_reader(bytes_reader, self.parsed_schema)
            
            # Store raw message for potential DLQ usage
            record['_raw_message'] = raw_message
            
            # Apply all processors in sequence
            for processor in self.processors:
                record = processor(record)
                
            return record
        except Exception as e:
            logging.error(f"Error processing message: {e}")
            return {'_raw_message': raw_message, 'error': str(e)}
    
    # Common processors that can be added to the pipeline
    def convert_decimal_fields(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Convert logical decimal types to Python Decimal objects"""
        # Implementation details...
        return record
    
    def convert_date_fields(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Convert logical date types to Python datetime objects"""
        # Implementation details...
        return record