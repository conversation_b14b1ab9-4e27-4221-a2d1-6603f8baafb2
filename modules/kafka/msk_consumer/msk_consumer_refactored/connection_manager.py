from contextlib import contextmanager
from typing import Dict, Any
from psycopg2.pool import SimpleConnectionPool

class ConnectionManager:
    def __init__(self):
        self.pools: Dict[str, SimpleConnectionPool] = {}
    
    def create_pool(self, name: str, connection_string: str, min_conn: int = 1, max_conn: int = 10):
        self.pools[name] = SimpleConnectionPool(min_conn, max_conn, connection_string)
    
    @contextmanager
    def get_connection(self, pool_name: str):
        pool = self.pools.get(pool_name)
        if not pool:
            raise ValueError(f"Connection pool {pool_name} not found")
        
        conn = pool.getconn()
        try:
            yield conn
        finally:
            pool.putconn(conn)
    
    def close_all(self):
        for pool in self.pools.values():
            if not pool.closed:
                pool.closeall()