#!/usr/bin/env python3
"""
Unit tests for postgres_batch_utils.py module.
Tests batch processing, data adaptation, and error handling.
"""

import unittest
from unittest.mock import patch, MagicMock, call
from decimal import Decimal
from datetime import datetime
import json
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestPostgresBatchUtils(unittest.TestCase):
    """Test cases for postgres_batch_utils module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_batch = [
            {
                'id': 1,
                'name': 'test1',
                '_kafka_offset': 100,
                '_raw_message': b'raw_message_1'
            },
            {
                'id': 2,
                'name': 'test2',
                '_kafka_offset': 101,
                '_raw_message': b'raw_message_2'
            }
        ]
        self.table_columns = ['id', 'name']

    def test_bytes_encoder_bytes(self):
        """Test BytesEncoder with bytes object."""
        from postgres_batch_utils import BytesEncoder
        
        encoder = BytesEncoder()
        test_bytes = b'\x01\x02\x03'
        
        result = encoder.default(test_bytes)
        
        self.assertEqual(result, '010203')

    def test_bytes_encoder_decimal(self):
        """Test BytesEncoder with Decimal object."""
        from postgres_batch_utils import BytesEncoder
        
        encoder = BytesEncoder()
        test_decimal = Decimal('123.45')
        
        result = encoder.default(test_decimal)
        
        self.assertEqual(result, 123.45)

    def test_bytes_encoder_datetime(self):
        """Test BytesEncoder with datetime object."""
        from postgres_batch_utils import BytesEncoder
        
        encoder = BytesEncoder()
        test_datetime = datetime(2021, 1, 1, 12, 0, 0)
        
        result = encoder.default(test_datetime)
        
        self.assertEqual(result, '2021-01-01T12:00:00')

    def test_make_json_serializable_dict(self):
        """Test make_json_serializable with dictionary."""
        from postgres_batch_utils import make_json_serializable
        
        test_dict = {
            'bytes_field': b'\x01\x02',
            'decimal_field': Decimal('123.45'),
            'datetime_field': datetime(2021, 1, 1),
            'normal_field': 'string'
        }
        
        result = make_json_serializable(test_dict)
        
        self.assertEqual(result['bytes_field'], '0102')
        self.assertEqual(result['decimal_field'], 123.45)
        self.assertEqual(result['datetime_field'], '2021-01-01T00:00:00')
        self.assertEqual(result['normal_field'], 'string')

    def test_make_json_serializable_list(self):
        """Test make_json_serializable with list."""
        from postgres_batch_utils import make_json_serializable
        
        test_list = [b'\x01\x02', Decimal('123.45'), 'string']
        
        result = make_json_serializable(test_list)
        
        self.assertEqual(result, ['0102', 123.45, 'string'])

    def test_adapt_value_for_postgres_dict(self):
        """Test adapt_value_for_postgres with dictionary."""
        from postgres_batch_utils import adapt_value_for_postgres
        
        test_dict = {'key': 'value', 'number': 123}
        
        result = adapt_value_for_postgres(test_dict)
        
        self.assertIsInstance(result, str)
        self.assertIn('key', result)
        self.assertIn('value', result)

    def test_adapt_value_for_postgres_variable_scale_decimal(self):
        """Test adapt_value_for_postgres with VariableScaleDecimal."""
        from postgres_batch_utils import adapt_value_for_postgres
        
        decimal_dict = {
            'scale': 2,
            'value': (12345).to_bytes(4, byteorder='big', signed=True)
        }
        
        result = adapt_value_for_postgres(decimal_dict)
        
        self.assertEqual(result, Decimal('123.45'))

    def test_adapt_value_for_postgres_bytes(self):
        """Test adapt_value_for_postgres with bytes."""
        from postgres_batch_utils import adapt_value_for_postgres
        
        test_bytes = b'\x01\x02\x03'
        
        result = adapt_value_for_postgres(test_bytes)
        
        self.assertEqual(result, '010203')

    def test_adapt_value_for_postgres_decimal(self):
        """Test adapt_value_for_postgres with Decimal."""
        from postgres_batch_utils import adapt_value_for_postgres
        
        test_decimal = Decimal('123.45')
        
        result = adapt_value_for_postgres(test_decimal)
        
        self.assertEqual(result, 123.45)

    @patch('postgres_batch_utils.get_dlq_topic_name')
    def test_send_to_dlq_success(self, mock_get_dlq_topic):
        """Test successful DLQ message sending."""
        from postgres_batch_utils import send_to_dlq
        
        mock_get_dlq_topic.return_value = 'test-topic-DLQ'
        mock_producer = MagicMock()
        
        send_to_dlq(b'test_message', 'test-topic', mock_producer)
        
        mock_producer.produce.assert_called_once()
        call_args = mock_producer.produce.call_args
        self.assertEqual(call_args[1]['value'], b'test_message')

    @patch('postgres_batch_utils.ENABLE_DLQ', False)
    def test_send_to_dlq_disabled(self):
        """Test DLQ sending when disabled."""
        from postgres_batch_utils import send_to_dlq
        
        mock_producer = MagicMock()
        
        send_to_dlq(b'test_message', 'test-topic', mock_producer)
        
        mock_producer.produce.assert_not_called()

    @patch('postgres_batch_utils.db_connection')
    @patch('postgres_batch_utils.handle_discarded_message')
    def test_insert_batch_into_postgres_success(self, mock_handle_discarded, mock_db_conn):
        """Test successful batch insertion."""
        from postgres_batch_utils import insert_batch_into_postgres
        
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_db_conn.return_value.__enter__.return_value = mock_conn
        
        insert_batch_into_postgres(
            self.test_batch,
            'test_schema',
            'test_table',
            self.table_columns,
            'test-topic',
            'discarded.txt'
        )
        
        # Should attempt batch insertion
        mock_cursor.executemany.assert_called_once()
        mock_conn.commit.assert_called_once()

    @patch('postgres_batch_utils.db_connection')
    @patch('postgres_batch_utils.handle_discarded_message')
    def test_insert_batch_into_postgres_batch_failure_individual_success(self, mock_handle_discarded, mock_db_conn):
        """Test batch failure with individual record success."""
        from postgres_batch_utils import insert_batch_into_postgres
        
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_db_conn.return_value.__enter__.return_value = mock_conn
        
        # Batch insertion fails, individual insertions succeed
        mock_cursor.executemany.side_effect = Exception("Batch error")
        mock_cursor.execute.return_value = None
        
        insert_batch_into_postgres(
            self.test_batch,
            'test_schema',
            'test_table',
            self.table_columns,
            'test-topic',
            'discarded.txt'
        )
        
        # Should attempt batch first, then individual
        mock_cursor.executemany.assert_called_once()
        self.assertEqual(mock_cursor.execute.call_count, len(self.test_batch))

    @patch('postgres_batch_utils.db_connection')
    @patch('postgres_batch_utils.handle_discarded_message')
    @patch('postgres_batch_utils.send_to_dlq')
    def test_insert_batch_into_postgres_individual_failure(self, mock_send_dlq, mock_handle_discarded, mock_db_conn):
        """Test individual record failure handling."""
        from postgres_batch_utils import insert_batch_into_postgres
        
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_db_conn.return_value.__enter__.return_value = mock_conn
        mock_producer = MagicMock()
        
        # Batch insertion fails, individual insertions also fail
        mock_cursor.executemany.side_effect = Exception("Batch error")
        mock_cursor.execute.side_effect = Exception("Individual error")
        
        insert_batch_into_postgres(
            self.test_batch,
            'test_schema',
            'test_table',
            self.table_columns,
            'test-topic',
            'discarded.txt',
            mock_producer
        )
        
        # Should handle discarded messages and send to DLQ
        self.assertEqual(mock_handle_discarded.call_count, len(self.test_batch))
        self.assertEqual(mock_send_dlq.call_count, len(self.test_batch))

    def test_insert_batch_into_postgres_empty_batch(self):
        """Test handling of empty batch."""
        from postgres_batch_utils import insert_batch_into_postgres
        
        # Should return early without doing anything
        result = insert_batch_into_postgres(
            [],
            'test_schema',
            'test_table',
            self.table_columns,
            'test-topic',
            'discarded.txt'
        )
        
        self.assertIsNone(result)

    @patch('postgres_batch_utils.db_connection')
    @patch('postgres_batch_utils.handle_discarded_message')
    def test_insert_batch_into_postgres_preparation_failure(self, mock_handle_discarded, mock_db_conn):
        """Test handling of record preparation failure."""
        from postgres_batch_utils import insert_batch_into_postgres
        
        # Create a batch with problematic data that will fail adaptation
        problematic_batch = [
            {
                'id': 1,
                'problematic_field': object(),  # This will fail adaptation
                '_kafka_offset': 100,
                '_raw_message': b'raw_message_1'
            }
        ]
        
        insert_batch_into_postgres(
            problematic_batch,
            'test_schema',
            'test_table',
            ['id', 'problematic_field'],
            'test-topic',
            'discarded.txt'
        )
        
        # Should handle the preparation failure
        mock_handle_discarded.assert_called()


if __name__ == '__main__':
    unittest.main()
