#!/usr/bin/env python3
"""
Unit tests for main.py module.
Tests command-line argument parsing and main entry point.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestMain(unittest.TestCase):
    """Test cases for main module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            "table_mapping": {
                "schema.table1": {
                    "topic": "test-topic",
                    "group_id": "test-group"
                }
            }
        }

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'schema.table1'])
    def test_main_without_local_schema_flag(self, mock_load_config, mock_consume_messages):
        """Test main execution without --use-local-schema flag."""
        mock_load_config.return_value = self.test_config
        
        # Import and run main
        import main
        
        mock_load_config.assert_called_once()
        mock_consume_messages.assert_called_once_with('schema.table1', False, self.test_config)

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'schema.table1', '--use-local-schema'])
    def test_main_with_local_schema_flag(self, mock_load_config, mock_consume_messages):
        """Test main execution with --use-local-schema flag."""
        mock_load_config.return_value = self.test_config
        
        # Import and run main
        import main
        
        mock_load_config.assert_called_once()
        mock_consume_messages.assert_called_once_with('schema.table1', True, self.test_config)

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'different.table'])
    def test_main_with_different_table(self, mock_load_config, mock_consume_messages):
        """Test main execution with different table name."""
        mock_load_config.return_value = self.test_config
        
        # Import and run main
        import main
        
        mock_load_config.assert_called_once()
        mock_consume_messages.assert_called_once_with('different.table', False, self.test_config)

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py'])
    def test_main_missing_table_argument(self, mock_load_config, mock_consume_messages):
        """Test main execution with missing table argument."""
        mock_load_config.return_value = self.test_config
        
        # This should raise SystemExit due to missing required argument
        with self.assertRaises(SystemExit):
            import main

        mock_load_config.assert_not_called()
        mock_consume_messages.assert_not_called()

    @patch('main.consume_messages')
    @patch('main.load_config')
    def test_argument_parser_configuration(self, mock_load_config, mock_consume_messages):
        """Test argument parser configuration."""
        import argparse
        from unittest.mock import patch
        
        # Test the argument parser directly
        with patch('sys.argv', ['main.py', '--help']):
            with self.assertRaises(SystemExit):
                # This will trigger the help and exit
                import main

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'schema.table1', '--use-local-schema', '--extra-arg'])
    def test_main_with_unknown_argument(self, mock_load_config, mock_consume_messages):
        """Test main execution with unknown argument."""
        mock_load_config.return_value = self.test_config
        
        # This should raise SystemExit due to unknown argument
        with self.assertRaises(SystemExit):
            import main

        mock_load_config.assert_not_called()
        mock_consume_messages.assert_not_called()

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'schema.table1'])
    def test_config_loading_failure(self, mock_load_config, mock_consume_messages):
        """Test behavior when config loading fails."""
        mock_load_config.return_value = {}  # Empty config
        
        # Import and run main
        import main
        
        mock_load_config.assert_called_once()
        mock_consume_messages.assert_called_once_with('schema.table1', False, {})

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'schema.table1'])
    def test_consume_messages_exception(self, mock_load_config, mock_consume_messages):
        """Test behavior when consume_messages raises an exception."""
        mock_load_config.return_value = self.test_config
        mock_consume_messages.side_effect = Exception("Consumer error")
        
        # The exception should propagate
        with self.assertRaises(Exception) as context:
            import main
        
        self.assertIn("Consumer error", str(context.exception))

    def test_module_name_check(self):
        """Test that main code only runs when module is executed directly."""
        # When importing the module (not running as main), the main code shouldn't execute
        with patch('main.consume_messages') as mock_consume_messages:
            with patch('main.load_config') as mock_load_config:
                # Import without setting __name__ to '__main__'
                import importlib
                import main
                
                # The main execution code should not have run
                mock_load_config.assert_not_called()
                mock_consume_messages.assert_not_called()

    @patch('main.consume_messages')
    @patch('main.load_config')
    def test_table_name_format_validation(self, mock_load_config, mock_consume_messages):
        """Test that table names in schema.table format are accepted."""
        mock_load_config.return_value = self.test_config
        
        test_cases = [
            'schema.table',
            'my_schema.my_table',
            'schema123.table456',
            'schema_name.table_name'
        ]
        
        for table_name in test_cases:
            with patch('sys.argv', ['main.py', table_name]):
                # Reset mocks
                mock_load_config.reset_mock()
                mock_consume_messages.reset_mock()
                
                # Import and run main
                import importlib
                import main
                importlib.reload(main)
                
                mock_consume_messages.assert_called_once_with(table_name, False, self.test_config)

    @patch('main.consume_messages')
    @patch('main.load_config')
    @patch('sys.argv', ['main.py', 'schema.table1', '--use-local-schema'])
    def test_boolean_flag_parsing(self, mock_load_config, mock_consume_messages):
        """Test that boolean flags are parsed correctly."""
        mock_load_config.return_value = self.test_config
        
        # Import and run main
        import main
        
        # Verify that the flag was parsed as True
        mock_consume_messages.assert_called_once_with('schema.table1', True, self.test_config)


if __name__ == '__main__':
    unittest.main()
