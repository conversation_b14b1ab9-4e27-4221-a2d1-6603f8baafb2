#!/usr/bin/env python3
"""
Unit tests for main.py module.
Tests command-line argument parsing and main entry point.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestMain(unittest.TestCase):
    """Test cases for main module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            "table_mapping": {
                "schema.table1": {
                    "topic": "test-topic",
                    "group_id": "test-group"
                }
            }
        }

    @patch('main.consume_messages')
    @patch('main.load_config')
    def test_main_without_local_schema_flag(self, mock_load_config, mock_consume_messages):
        """Test main execution without --use-local-schema flag."""
        mock_load_config.return_value = self.test_config

        # Test the argument parser directly
        import main
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # Parse test arguments
        args = parser.parse_args(['schema.table1'])

        # Verify parsing
        self.assertEqual(args.table_name, 'schema.table1')
        self.assertFalse(args.use_local_schema)

    def test_main_with_local_schema_flag(self):
        """Test main execution with --use-local-schema flag."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # Parse test arguments
        args = parser.parse_args(['schema.table1', '--use-local-schema'])

        # Verify parsing
        self.assertEqual(args.table_name, 'schema.table1')
        self.assertTrue(args.use_local_schema)

    def test_main_with_different_table(self):
        """Test main execution with different table name."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # Parse test arguments
        args = parser.parse_args(['different.table'])

        # Verify parsing
        self.assertEqual(args.table_name, 'different.table')
        self.assertFalse(args.use_local_schema)

    def test_main_missing_table_argument(self):
        """Test main execution with missing table argument."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # This should raise SystemExit due to missing required argument
        with self.assertRaises(SystemExit):
            parser.parse_args([])

    def test_argument_parser_help(self):
        """Test argument parser help functionality."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # Test the help functionality
        with self.assertRaises(SystemExit):
            parser.parse_args(['--help'])

    def test_main_with_unknown_argument(self):
        """Test main execution with unknown argument."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # This should raise SystemExit due to unknown argument
        with self.assertRaises(SystemExit):
            parser.parse_args(['schema.table1', '--use-local-schema', '--extra-arg'])

    def test_table_name_format_validation(self):
        """Test that table names in schema.table format are accepted."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        test_cases = [
            'schema.table',
            'my_schema.my_table',
            'schema123.table456',
            'schema_name.table_name'
        ]

        for table_name in test_cases:
            args = parser.parse_args([table_name])
            self.assertEqual(args.table_name, table_name)
            self.assertFalse(args.use_local_schema)

    def test_boolean_flag_parsing(self):
        """Test that boolean flags are parsed correctly."""
        import argparse

        # Create parser like in main
        parser = argparse.ArgumentParser(description="Kafka Consumer for MSK")
        parser.add_argument("table_name", help="Table name in format schema.table")
        parser.add_argument("--use-local-schema", action="store_true", help="Use local schema file instead of schema registry")

        # Parse test arguments with flag
        args = parser.parse_args(['schema.table1', '--use-local-schema'])

        # Verify that the flag was parsed as True
        self.assertEqual(args.table_name, 'schema.table1')
        self.assertTrue(args.use_local_schema)


if __name__ == '__main__':
    unittest.main()
