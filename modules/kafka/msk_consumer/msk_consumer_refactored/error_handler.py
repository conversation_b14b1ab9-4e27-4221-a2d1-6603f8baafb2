import logging
from typing import Dict, Any, Optional
from confluent_kafka import Producer

class ErrorHandler:
    def __init__(self, dlq_producer: Optional[Producer] = None, enable_db_logging: bool = True, enable_file_logging: bool = False):
        self.dlq_producer = dlq_producer
        self.enable_db_logging = enable_db_logging
        self.enable_file_logging = enable_file_logging
    
    def handle_message_error(self, message: Dict[str, Any], error: Exception, topic: str, offset: int, discarded_messages_file: str) -> None:
        """Centralized error handling for message processing errors"""
        from logging_utils import handle_discarded_message
        from kafka_utils import send_to_dlq
        
        logging.error(f"Error processing message at offset {offset}: {error}")
        
        if self.dlq_producer and '_raw_message' in message:
            send_to_dlq(message['_raw_message'], topic, self.dlq_producer)
        
        handle_discarded_message(message, str(error), topic, offset, discarded_messages_file)