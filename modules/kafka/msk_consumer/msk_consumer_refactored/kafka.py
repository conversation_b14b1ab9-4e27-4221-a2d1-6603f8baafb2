import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from confluent_kafka import Consumer, Producer
from confluent_kafka.admin import AdminClient, NewTopic
from confluent_kafka.schema_registry import SchemaRegistryClient

class KafkaService:
    def __init__(self, bootstrap_servers: str, schema_registry_url: Optional[str] = None):
        self.bootstrap_servers = bootstrap_servers
        self.schema_registry_url = schema_registry_url
        self.schema_registry_client = None
        if schema_registry_url:
            self.schema_registry_client = SchemaRegistryClient({'url': schema_registry_url})
    
    def create_consumer(self, group_id: str, auto_offset_reset: str = "earliest") -> Consumer:
        return Consumer({
            "bootstrap.servers": self.bootstrap_servers,
            "group.id": group_id,
            "auto.offset.reset": auto_offset_reset,
            "security.protocol": "PLAINTEXT",
            "enable.auto.commit": False
        })
    
    def create_producer(self) -> Producer:
        return Producer({
            "bootstrap.servers": self.bootstrap_servers,
            "security.protocol": "PLAINTEXT"
        })
    
    def fetch_schema(self, topic: str) -> Optional[Dict[str, Any]]:
        if not self.schema_registry_client:
            logging.error("Schema registry client not initialized")
            return None
            
        try:
            subject = f"{topic}-value"
            schema = self.schema_registry_client.get_latest_version(subject)
            return json.loads(schema.schema.schema_str)
        except Exception as e:
            logging.error(f"Failed to fetch schema from registry: {e}")
            return None
    
    def ensure_topic_exists(self, topic: str, num_partitions: int = 1, replication_factor: int = 1) -> bool:
        try:
            admin_client = AdminClient({"bootstrap.servers": self.bootstrap_servers})
            topics = admin_client.list_topics(timeout=10).topics
            
            if topic in topics:
                logging.info(f"Topic {topic} already exists")
                return True
                
            new_topics = [NewTopic(topic, num_partitions, replication_factor)]
            result = admin_client.create_topics(new_topics)
            
            for topic, future in result.items():
                try:
                    future.result()
                    logging.info(f"Topic {topic} created")
                except Exception as e:
                    logging.error(f"Failed to create topic {topic}: {e}")
                    return False
            return True
        except Exception as e:
            logging.error(f"Error ensuring topic exists: {e}")
            return False