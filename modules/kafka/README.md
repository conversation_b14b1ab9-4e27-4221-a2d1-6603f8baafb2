# Kafka Consumer

## Overview of Kafka Consumer

### Pipeline Flow Diagram
![Kafka Pipeline Flow](./kafka.png)

### What This Consumer Does
- Consumes messages from Kafka topics.
- Validates messages against Avro schemas (from Schema Registry or local files).
- Inserts or updates data in PostgreSQL using efficient batch operations.
- Tracks Kafka offsets for reliable error handling and message tracking.
- Automatically derives day_id from op_ts timestamps when needed.
- Provides fallback mechanisms for handling batch failures.
- Supports configurable batch wait times per topic.
- Handles special data types like decimals and dates.
- Supports dynamic topic-table mapping via `config.json`.

### Key Components
| Component      | Responsibility |
|---------------|----------------|
| `consumer.py` | Main consumer script – connects to Kafka, processes messages, inserts into DB. |
| `config.json` | Maps Kafka topics to PostgreSQL tables. |
| `schema files (.avsc)` | Defines the schema for validation. |

## How the Consumer Reads and Writes Data
### Kafka Consumer Flow
1. Connects to Kafka (`consumer.py`).
2. Reads messages from a Kafka topic.
3. Decodes Avro messages using Schema Registry or local schema files.
4. Stores Kafka offset in each message for tracking and error handling.
5. Accumulates messages in a batch until batch size limit or wait time is reached.
6. Derives day_id from op_ts timestamp if needed.
7. Performs batch INSERT/UPDATE operations to PostgreSQL.
8. Falls back to individual record processing if batch operation fails.
9. Logs any failed records with detailed error information.

### Insert/Update Logic
- Uses PostgreSQL `ON CONFLICT` for efficient upserts:
  ```sql
  INSERT INTO "schema_name"."table_name" (column1, column2)
  VALUES (%s, %s)
  ON CONFLICT (primary_key_columns) DO UPDATE
  SET column1 = EXCLUDED.column1, column2 = EXCLUDED.column2
  WHERE "schema_name"."table_name"."op_ts" < EXCLUDED."op_ts"
  ```
- Ensures data consistency by only updating if the new record is newer (based on op_ts).
- Processes messages in configurable batch sizes for efficiency.
- Uses connection pooling for better database performance.

## How to Add a New Kafka Topic & Configure It
### Steps to Add a New Kafka Topic
#### Step 1: Edit `config.json`
- Define the topic-to-table mapping in `config.json`:
  ```json
  {
      "my_schema.my_table": {
          "topic": "new_kafka_topic",
          "schema_id": 101,
          "local_schema_file": "schemas/new_schema.avsc",
          "write_batch_size": 500,
          "batch_wait": 2.0,
          "group_id": "new_consumer_group"
      }
  }
  ```
- **What Each Field Means:**
  - `"topic"` – Kafka topic name.
  - `"schema_id"` – Schema Registry ID for Avro validation.
  - `"local_schema_file"` – Path to Avro schema file (for local schema validation).
  - `"write_batch_size"` – Number of messages per batch insert (default: 500).
  - `"batch_wait"` – Time in seconds to wait before processing a partial batch (default: 2.0).
  - `"group_id"` – Kafka Consumer Group ID.

#### Step 2: Ensure Schema Exists in Schema Registry
```bash
curl http://schema-registry:8081/subjects/new_kafka_topic-value/versions
```
- Returns schema details if registered.
- If missing, register it manually.

#### Step 3: Restart the Consumer
```bash
python consumer.py new_schema.new_table
```
- Now consuming from the new topic!

## Running the Kafka Consumer
### Run Consumer Normally
```bash
python consumer.py my_schema.my_table
```
- Starts consuming messages using Schema Registry.
- Inserts or updates data in PostgreSQL.

### Run Consumer with Local Schema
```bash
python consumer.py my_schema.my_table --use-local-schema
```
- Uses local schema file instead of Schema Registry.
- Useful for development or when Schema Registry is unavailable.

### Run Consumer with Debugging
```bash
python consumer.py my_schema.my_table --log-level DEBUG
```
- Enables detailed logs for debugging.

### Restarting the Consumer
```bash
pkill -f consumer.py  # Stop the running consumer
python consumer.py my_schema.my_table
```

## Q&A / Next Steps
### Questions from Team?
- Clarify how the consumer processes messages.
- Explain how to add new topics efficiently.

### Next Steps
- Test Kafka message ingestion end-to-end.
- Validate schema enforcement with Avro.
- Optimize batch processing for PostgreSQL performance.

## Testing Connectivity

The module includes scripts for testing connectivity to Kafka and PostgreSQL:

### Test All Connections
```bash
python test_connectivity.py
```
- Tests connectivity to PostgreSQL and all Kafka topics defined in config.json
- Uses local schema files instead of Schema Registry
- Provides a detailed summary of successful and failed connections
- Does not commit any offsets, so it won't affect consumer groups

### Test a Specific Topic
```bash
python test_topic.py <table_name> [--messages <num>] [--timeout <seconds>]
```
- Tests connectivity to a specific Kafka topic
- Reads and displays message content without committing offsets
- Shows derived day_id values for verification
- Allows specifying how many messages to read and timeout duration

## DLQ Configuration

In your `config.json`, you can configure DLQ behavior:

```json
{
  "enable_dlq": true,
  "dlq_suffix": "-DLQ"
}
```

- `enable_dlq`: Enable or disable the DLQ feature (default: true)
- `dlq_suffix`: The suffix to append to the original topic name to create the DLQ topic name (default: "-DLQ")

## DLQ Topic Naming

DLQ topics are named using the format: `{original_topic}{dlq_suffix}`

For example, if your source topic is `cdc-v1-FCUBS.ADFCUBSGC.STTM_CUSTOMER` and the DLQ suffix is `-DLQ`, the DLQ topic will be:
`cdc-v1-FCUBS.ADFCUBSGC.STTM_CUSTOMER-DLQ`

This naming convention makes it easy to associate DLQ topics with their source topics.

## Summary
✅ Kafka Consumer Reads Messages from Topics
✅ Inserts/Updates Data into PostgreSQL Using Efficient Batch Operations
✅ Supports Schema Validation Using Avro (Schema Registry or Local Files)
✅ Tracks Kafka Offsets for Reliable Error Handling
✅ Automatically Derives day_id from op_ts Timestamps
✅ Provides Fallback Mechanisms for Batch Failures
✅ Supports Configurable Batch Wait Times per Topic
✅ Handles Special Data Types (Decimals, Dates)
✅ Includes Connectivity Testing Scripts
✅ Easily Add New Topics via `config.json`
✅ Runs with Simple CLI Command

